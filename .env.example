# Set only one of the following API keys, depending on your preferred service
OPENAI_API_KEY=

# OpenRouter.ai - https://openrouter.ai/keys
OPENROUTER_API_KEY=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
OPENROUTER_API_BASE=https://openrouter.ai/api/v1

ANTHROPIC_API_KEY=
GOOGLE_API_KEY=
TAVILY_API_KEY=
LANGSMITH_API_KEY=
LANGSMITH_PROJECT=
LANGSMITH_TRACING=

# Only necessary for Open Agent Platform
SUPABASE_KEY=
SUPABASE_URL=
# Should be set to true for a production deployment on Open Agent Platform. Should be set to false otherwise, such as for local development.
GET_API_KEYS_FROM_CONFIG=false