#!/usr/bin/env python3
"""
Configuration for using Open Deep Research with OpenRouter models.
This script helps configure the models to use OpenRouter instead of direct OpenAI API.
"""

# Recommended OpenRouter models for different tasks
OPENROUTER_CONFIG = {
    "summarization_model": "qwen/qwen3-235b-a22b-07-25:free",  # Fast and efficient for summarization
    "research_model": "x-ai/grok-4",            # Strong reasoning for research
    "compression_model": "mistralai/mistral-small-3.2-24b-instruct:free",    # Efficient for compression
    "final_report_model": "qwen/qwen3-235b-a22b-07-25:free",        # High quality for final reports
}

# Alternative high-quality models you can try:
ALTERNATIVE_MODELS = {
    "summarization_model": "anthropic/claude-3.5-sonnet",
    "research_model": "google/gemini-2.5-pro", 
    "research_model": "deepseek/deepseek-r1-0528:free",
    "compression_model": "deepseek/deepseek-r1-0528:free",
    "final_report_model": "deepseek/deepseek-r1-0528:free",
    
}

def print_config_instructions():
    """Print instructions for configuring OpenRouter."""
    print("=" * 60)
    print("🚀 OPEN DEEP RESEARCH - OPENROUTER CONFIGURATION")
    print("=" * 60)
    print("\n1. 📝 Create a .env file by copying .env.example:")
    print("   cp .env.example .env")
    print("\n2. ✏️ Edit your .env file and add your API keys:")
    print("   - Set OPENROUTER_API_KEY with your key from https://openrouter.ai/keys")
    print("   - The OPENROUTER_API_BASE is already set for you.")
    print("   - Set TAVILY_API_KEY for web search capabilities.")
    print("\n3. 🔧 Recommended Model Configuration (for LangGraph Studio):")
    for key, model in OPENROUTER_CONFIG.items():
        print(f"   - {key}: {model}")
    
    print("\n3. 🌐 To start the LangGraph server:")
    print("   langgraph dev --allow-blocking")
    print("\n4. 🎨 Open LangGraph Studio:")
    print("   https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024")
    print("\n5. 💡 Configure models in the Studio UI:")
    print("   Use the model names from step 2 above in the configuration panel")
    print("\n" + "=" * 60)

if __name__ == "__main__":
    print_config_instructions()
